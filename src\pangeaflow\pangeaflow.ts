/**
 * PangeaFlow - Next-generation agentic AI workflow system
 * 
 * A reactive, event-driven framework for building scalable AI agent workflows
 * with built-in observability, error recovery, and dynamic orchestration.
 * 
 * Key improvements over traditional approaches:
 * - Event-driven architecture for loose coupling
 * - Reactive state management with automatic invalidation
 * - Built-in telemetry and observability
 * - Dynamic workflow adaptation based on runtime conditions
 * - Memory-efficient streaming for large datasets
 * - Hierarchical error boundaries with recovery strategies
 */

// ============================================================================
// CORE TYPES & INTERFACES
// ============================================================================

/** Unique identifier for workflow components */
type ComponentId = string & { readonly __brand: unique symbol };

/** Event types for workflow communication */
interface WorkflowEvent<T = unknown> {
  readonly id: string;
  readonly type: string;
  readonly timestamp: number;
  readonly source: ComponentId;
  readonly payload: T;
  readonly correlationId?: string;
}

/** Context passed through workflow execution */
interface ExecutionContext {
  readonly id: string;
  readonly startTime: number;
  readonly metadata: Record<string, unknown>;
  readonly trace: string[];
  readonly state: Map<string, unknown>;
  readonly events: WorkflowEvent[];
}

/** Result of component execution */
interface ExecutionResult<T = unknown> {
  readonly success: boolean;
  readonly data?: T;
  readonly error?: Error;
  readonly events: WorkflowEvent[];
  readonly nextActions: string[];
  readonly metadata: Record<string, unknown>;
}

/** Telemetry data for monitoring */
interface TelemetryData {
  readonly componentId: ComponentId;
  readonly operation: string;
  readonly duration: number;
  readonly success: boolean;
  readonly metadata: Record<string, unknown>;
}

// ============================================================================
// EVENT SYSTEM
// ============================================================================

class EventBus {
  private readonly listeners = new Map<string, Set<(event: WorkflowEvent) => void>>();
  
  emit<T>(type: string, payload: T, source: ComponentId, correlationId?: string): void {
    const event: WorkflowEvent<T> = {
      id: crypto.randomUUID(),
      type,
      timestamp: Date.now(),
      source,
      payload,
      correlationId,
    };
    
    this.listeners.get(type)?.forEach(listener => {
      try {
        listener(event);
      } catch (error) {
        console.error(`Event listener error for ${type}:`, error);
      }
    });
  }
  
  on(type: string, listener: (event: WorkflowEvent) => void): () => void {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, new Set());
    }
    this.listeners.get(type)!.add(listener);
    
    return () => this.listeners.get(type)?.delete(listener);
  }
  
  once(type: string, listener: (event: WorkflowEvent) => void): () => void {
    const wrapper = (event: WorkflowEvent) => {
      listener(event);
      this.listeners.get(type)?.delete(wrapper);
    };
    return this.on(type, wrapper);
  }
}

// ============================================================================
// REACTIVE STATE MANAGEMENT
// ============================================================================

class ReactiveState<T> {
  private value: T;
  private readonly subscribers = new Set<(value: T, previous: T) => void>();
  private readonly computedCache = new Map<string, unknown>();
  
  constructor(initialValue: T) {
    this.value = initialValue;
  }
  
  get(): T {
    return this.value;
  }
  
  set(newValue: T | ((prev: T) => T)): void {
    const previous = this.value;
    this.value = typeof newValue === 'function' ? (newValue as Function)(previous) : newValue;
    
    if (this.value !== previous) {
      this.computedCache.clear();
      this.subscribers.forEach(subscriber => {
        try {
          subscriber(this.value, previous);
        } catch (error) {
          console.error('State subscriber error:', error);
        }
      });
    }
  }
  
  subscribe(callback: (value: T, previous: T) => void): () => void {
    this.subscribers.add(callback);
    return () => this.subscribers.delete(callback);
  }
  
  computed<R>(key: string, fn: (value: T) => R): R {
    if (!this.computedCache.has(key)) {
      this.computedCache.set(key, fn(this.value));
    }
    return this.computedCache.get(key) as R;
  }
}

// ============================================================================
// TELEMETRY & OBSERVABILITY
// ============================================================================

class TelemetryCollector {
  private readonly metrics: TelemetryData[] = [];
  private readonly maxMetrics = 10000;
  
  record(data: TelemetryData): void {
    this.metrics.push(data);
    if (this.metrics.length > this.maxMetrics) {
      this.metrics.splice(0, this.metrics.length - this.maxMetrics);
    }
  }
  
  getMetrics(filter?: Partial<TelemetryData>): TelemetryData[] {
    if (!filter) return [...this.metrics];
    
    return this.metrics.filter(metric =>
      Object.entries(filter).every(([key, value]) =>
        metric[key as keyof TelemetryData] === value
      )
    );
  }
  
  getStats(componentId?: ComponentId): {
    totalExecutions: number;
    successRate: number;
    avgDuration: number;
    errorRate: number;
  } {
    const relevant = componentId 
      ? this.metrics.filter(m => m.componentId === componentId)
      : this.metrics;
    
    const total = relevant.length;
    const successful = relevant.filter(m => m.success).length;
    const totalDuration = relevant.reduce((sum, m) => sum + m.duration, 0);
    
    return {
      totalExecutions: total,
      successRate: total > 0 ? successful / total : 0,
      avgDuration: total > 0 ? totalDuration / total : 0,
      errorRate: total > 0 ? (total - successful) / total : 0,
    };
  }
}

// ============================================================================
// CORE AGENT COMPONENT
// ============================================================================

abstract class AgentComponent {
  readonly id: ComponentId;
  protected readonly eventBus: EventBus;
  protected readonly telemetry: TelemetryCollector;
  protected readonly state: ReactiveState<Record<string, unknown>>;
  
  constructor(
    id: string,
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    initialState: Record<string, unknown> = {}
  ) {
    this.id = id as ComponentId;
    this.eventBus = eventBus;
    this.telemetry = telemetry;
    this.state = new ReactiveState(initialState);
  }
  
  abstract execute(context: ExecutionContext): Promise<ExecutionResult>;
  
  protected emit<T>(type: string, payload: T, correlationId?: string): void {
    this.eventBus.emit(type, payload, this.id, correlationId);
  }
  
  protected async withTelemetry<T>(
    operation: string,
    fn: () => Promise<T>
  ): Promise<T> {
    const start = performance.now();
    let success = true;
    let error: Error | undefined;
    
    try {
      return await fn();
    } catch (e) {
      success = false;
      error = e as Error;
      throw e;
    } finally {
      const duration = performance.now() - start;
      this.telemetry.record({
        componentId: this.id,
        operation,
        duration,
        success,
        metadata: { error: error?.message },
      });
    }
  }
}

// ============================================================================
// SPECIALIZED AGENT COMPONENTS
// ============================================================================

/** LLM-powered reasoning component */
class ReasoningAgent extends AgentComponent {
  constructor(
    id: string,
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    private readonly llmProvider: (prompt: string, context: Record<string, unknown>) => Promise<string>
  ) {
    super(id, eventBus, telemetry);
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('reasoning', async () => {
      const prompt = this.buildPrompt(context);
      const response = await this.llmProvider(prompt, context.metadata);
      
      const actions = this.parseActions(response);
      
      this.emit('reasoning.completed', { response, actions }, context.id);
      
      return {
        success: true,
        data: response,
        events: [],
        nextActions: actions,
        metadata: { promptLength: prompt.length, responseLength: response.length },
      };
    });
  }
  
  private buildPrompt(context: ExecutionContext): string {
    const contextStr = Array.from(context.state.entries())
      .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
      .join('\n');
    
    return `Context:\n${contextStr}\n\nTrace: ${context.trace.join(' -> ')}\n\nProvide your reasoning and next actions:`;
  }
  
  private parseActions(response: string): string[] {
    // Simple action parsing - can be enhanced with structured output
    const actionMatch = response.match(/ACTIONS?:\s*(.+)/i);
    if (!actionMatch) return ['continue'];
    
    return actionMatch[1]
      .split(',')
      .map(action => action.trim().toLowerCase())
      .filter(Boolean);
  }
}

/** Tool execution component */
class ToolAgent extends AgentComponent {
  constructor(
    id: string,
    eventBus: EventBus,
    telemetry: TelemetryCollector,
    private readonly tools: Map<string, (args: unknown) => Promise<unknown>>
  ) {
    super(id, eventBus, telemetry);
  }
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('tool_execution', async () => {
      const toolName = context.metadata.tool as string;
      const toolArgs = context.metadata.args;
      
      if (!toolName || !this.tools.has(toolName)) {
        throw new Error(`Tool '${toolName}' not found`);
      }
      
      const tool = this.tools.get(toolName)!;
      const result = await tool(toolArgs);
      
      this.emit('tool.executed', { toolName, result }, context.id);
      
      return {
        success: true,
        data: result,
        events: [],
        nextActions: ['continue'],
        metadata: { toolName, executedAt: Date.now() },
      };
    });
  }
}

/** Memory management component */
class MemoryAgent extends AgentComponent {
  private readonly shortTerm = new Map<string, unknown>();
  private readonly longTerm = new Map<string, unknown>();
  
  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    return this.withTelemetry('memory_operation', async () => {
      const operation = context.metadata.operation as string;
      const key = context.metadata.key as string;
      const value = context.metadata.value;
      const persistent = context.metadata.persistent as boolean;
      
      const storage = persistent ? this.longTerm : this.shortTerm;
      
      switch (operation) {
        case 'store':
          storage.set(key, value);
          this.emit('memory.stored', { key, persistent }, context.id);
          break;
        case 'retrieve':
          const retrieved = storage.get(key);
          this.emit('memory.retrieved', { key, found: retrieved !== undefined }, context.id);
          return {
            success: true,
            data: retrieved,
            events: [],
            nextActions: ['continue'],
            metadata: { operation, key },
          };
        case 'clear':
          if (key) {
            storage.delete(key);
          } else {
            storage.clear();
          }
          this.emit('memory.cleared', { key }, context.id);
          break;
      }
      
      return {
        success: true,
        data: undefined,
        events: [],
        nextActions: ['continue'],
        metadata: { operation, key },
      };
    });
  }
}

// ============================================================================
// WORKFLOW ORCHESTRATOR
// ============================================================================

class WorkflowOrchestrator {
  private readonly components = new Map<ComponentId, AgentComponent>();
  private readonly routes = new Map<string, ComponentId[]>();
  private readonly eventBus = new EventBus();
  private readonly telemetry = new TelemetryCollector();
  
  registerComponent(component: AgentComponent): this {
    this.components.set(component.id, component);
    return this;
  }
  
  defineRoute(action: string, componentIds: ComponentId[]): this {
    this.routes.set(action, componentIds);
    return this;
  }
  
  async execute(
    startAction: string,
    initialContext: Partial<ExecutionContext> = {}
  ): Promise<ExecutionResult[]> {
    const context: ExecutionContext = {
      id: crypto.randomUUID(),
      startTime: Date.now(),
      metadata: {},
      trace: [],
      state: new Map(),
      events: [],
      ...initialContext,
    };
    
    const results: ExecutionResult[] = [];
    const pendingActions = [startAction];
    const executedComponents = new Set<ComponentId>();
    
    while (pendingActions.length > 0) {
      const currentAction = pendingActions.shift()!;
      const componentIds = this.routes.get(currentAction) || [];
      
      for (const componentId of componentIds) {
        // Prevent infinite loops
        if (executedComponents.has(componentId)) continue;
        executedComponents.add(componentId);
        
        const component = this.components.get(componentId);
        if (!component) {
          console.warn(`Component ${componentId} not found for action ${currentAction}`);
          continue;
        }
        
        try {
          context.trace.push(componentId);
          const result = await component.execute(context);
          results.push(result);

          // Check if the result indicates failure
          if (!result.success && result.error) {
            // Emit error event for failed agent execution
            this.eventBus.emit('error', {
              componentId,
              action: currentAction,
              error: result.error,
              message: result.error.message,
              timestamp: Date.now()
            }, componentId);

            // Also emit step.failed event
            this.eventBus.emit('step.failed', {
              componentId,
              action: currentAction,
              error: result.error,
              message: result.error.message,
              timestamp: Date.now()
            }, componentId);
          } else if (result.success) {
            // Emit step.completed event for successful execution
            this.eventBus.emit('step.completed', {
              componentId,
              action: currentAction,
              result: result.data,
              timestamp: Date.now()
            }, componentId);
          }

          // Add new actions to pending queue
          pendingActions.push(...result.nextActions);

          // Update context state with result data
          if (result.data !== undefined) {
            context.state.set(componentId, result.data);
          }

          // Merge events
          context.events.push(...result.events);
          
        } catch (error) {
          // Emit error event for monitoring
          this.eventBus.emit('error', {
            componentId,
            action: currentAction,
            error: error as Error,
            message: (error as Error).message,
            timestamp: Date.now()
          }, componentId);

          // Also emit step.failed event
          this.eventBus.emit('step.failed', {
            componentId,
            action: currentAction,
            error: error as Error,
            message: (error as Error).message,
            timestamp: Date.now()
          }, componentId);

          const errorResult: ExecutionResult = {
            success: false,
            error: error as Error,
            events: [],
            nextActions: ['error'],
            metadata: { componentId, action: currentAction },
          };
          results.push(errorResult);

          // Handle error routing
          if (this.routes.has('error')) {
            pendingActions.push('error');
          }
        }
      }
    }
    
    return results;
  }
  
  getMetrics(): ReturnType<TelemetryCollector['getStats']> {
    return this.telemetry.getStats();
  }
  
  on(eventType: string, handler: (event: WorkflowEvent) => void): () => void {
    return this.eventBus.on(eventType, handler);
  }
}

// ============================================================================
// BUILDER & FACTORY
// ============================================================================

class WorkflowBuilder {
  private readonly orchestrator = new WorkflowOrchestrator();
  
  static create(): WorkflowBuilder {
    return new WorkflowBuilder();
  }
  
  addReasoningAgent(
    id: string,
    llmProvider: (prompt: string, context: Record<string, unknown>) => Promise<string>
  ): this {
    const agent = new ReasoningAgent(id, this.orchestrator['eventBus'], this.orchestrator['telemetry'], llmProvider);
    this.orchestrator.registerComponent(agent);
    return this;
  }
  
  addToolAgent(id: string, tools: Record<string, (args: unknown) => Promise<unknown>>): this {
    const toolMap = new Map(Object.entries(tools));
    const agent = new ToolAgent(id, this.orchestrator['eventBus'], this.orchestrator['telemetry'], toolMap);
    this.orchestrator.registerComponent(agent);
    return this;
  }
  
  addMemoryAgent(id: string): this {
    const agent = new MemoryAgent(id, this.orchestrator['eventBus'], this.orchestrator['telemetry']);
    this.orchestrator.registerComponent(agent);
    return this;
  }
  
  route(action: string, ...componentIds: string[]): this {
    this.orchestrator.defineRoute(action, componentIds as ComponentId[]);
    return this;
  }
  
  build(): WorkflowOrchestrator {
    return this.orchestrator;
  }
}

// ============================================================================
// STREAMING & BATCH PROCESSING
// ============================================================================

class StreamingWorkflow {
  constructor(private readonly orchestrator: WorkflowOrchestrator) {}
  
  async *processStream<T>(
    items: AsyncIterable<T>,
    startAction: string,
    batchSize: number = 10
  ): AsyncGenerator<ExecutionResult[], void, unknown> {
    const batch: T[] = [];
    
    for await (const item of items) {
      batch.push(item);
      
      if (batch.length >= batchSize) {
        const results = await this.processBatch(batch, startAction);
        yield results;
        batch.length = 0;
      }
    }
    
    // Process remaining items
    if (batch.length > 0) {
      const results = await this.processBatch(batch, startAction);
      yield results;
    }
  }
  
  private async processBatch<T>(batch: T[], startAction: string): Promise<ExecutionResult[]> {
    const promises = batch.map((item, index) =>
      this.orchestrator.execute(startAction, {
        metadata: { batchItem: item, batchIndex: index },
      })
    );
    
    const results = await Promise.all(promises);
    return results.flat();
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export {
  WorkflowBuilder,
  WorkflowOrchestrator,
  StreamingWorkflow,
  AgentComponent,
  ReasoningAgent,
  ToolAgent,
  MemoryAgent,
  EventBus,
  ReactiveState,
  TelemetryCollector,
};


export type {
  ComponentId,
  WorkflowEvent,
  ExecutionContext,
  ExecutionResult,
  TelemetryData,
};