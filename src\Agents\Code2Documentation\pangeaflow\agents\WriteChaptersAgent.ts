// src/Agents/Code2Documentation/pangeaflow/agents/WriteChaptersAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../../pangeaflow/pangeaflow';
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../../pocketflow/utils/buildPrompt";
import { WRITE_CHAPTER_PROMPT } from "../../prompts/writeChapters";
import { emitGraphStatus, emitProgress } from "../../utils/events";
import { getContentForIndices } from "../../utils/fileHelpers";
import { Abstraction, ChapterContent } from "../../types";

export class WriteChaptersAgent extends AgentComponent {
  private maxRetries: number;
  private waitTime: number;

  constructor(eventBus: any, telemetry: any, maxRetries = 5, waitTime = 20) {
    super('write-chapters', eventBus, telemetry);
    this.maxRetries = maxRetries;
    this.waitTime = waitTime;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    try {
      console.log('WriteChaptersAgent: Executing with context:', {
        metadata: context.sharedState,
        state: Array.from(context.nodeOutputs.entries())
      });

      // Extract parameters from context
      const {
        orderedAbstractions,
        abstractions,
        files,
        project_name,
        language,
        use_cache,
        user_id,
        tutorial_id
      } = context.sharedState;

      // Try to get orderedAbstractions from state if not in metadata
      let orderedAbstractionsFromState = null;
      if (!orderedAbstractions && context.nodeOutputs.has('order-chapters')) {
        const orderChaptersResult = context.nodeOutputs.get('order-chapters') as any;
        orderedAbstractionsFromState = orderChaptersResult?.orderedAbstractions;
        console.log('WriteChaptersAgent: Found orderedAbstractions in state:', orderedAbstractionsFromState);
      }

      // Try to get abstractions from state if not in metadata
      let abstractionsFromState = null;
      if (!abstractions && context.nodeOutputs.has('identify-abstractions')) {
        const identifyAbstractionsResult = context.nodeOutputs.get('identify-abstractions') as any;
        abstractionsFromState = identifyAbstractionsResult?.abstractions;
        console.log('WriteChaptersAgent: Found abstractions in state:', abstractionsFromState);
      }

      // Try to get files from state if not in metadata
      let filesFromState = null;
      if (!files && context.nodeOutputs.has('fetch-repo')) {
        const fetchRepoResult = context.nodeOutputs.get('fetch-repo') as any;
        filesFromState = fetchRepoResult?.files;
        console.log('WriteChaptersAgent: Found files in state:', filesFromState);
      }

      const orderedIndices = (orderedAbstractions as number[]) || orderedAbstractionsFromState || [];
      const abstractionsData = (abstractions as Abstraction[]) || abstractionsFromState || [];
      const files_data = (files as [string, string][]) || filesFromState || [];
      const lang = language as string ?? 'english';
      const useCache = use_cache as boolean ?? true;

      console.log('WriteChaptersAgent: Using orderedIndices:', orderedIndices);
      console.log('WriteChaptersAgent: Using abstractions:', abstractionsData);
      console.log('WriteChaptersAgent: Using files:', files_data);

      if (!orderedIndices || orderedIndices.length === 0) {
        throw new Error('No ordered abstractions found in context metadata or state');
      }

      if (!abstractionsData || abstractionsData.length === 0) {
        throw new Error('No abstractions found in context metadata or state');
      }

      if (!files_data || files_data.length === 0) {
        throw new Error('No files found in context metadata or state');
      }

      // Initialize chapters array if not present
      let chapterContents = (context.sharedState.chapterContents as ChapterContent[]) || [];
      let currentChapterIndex = chapterContents.length;

      // Check if all chapters are processed
      if (currentChapterIndex >= orderedIndices.length) {
        emitGraphStatus("WriteChapters", 100, "All chapters completed");
        return {
          success: true,
          output: {
            chapterContents: chapterContents
          },
          events: [],
          nextActions: ['combine-tutorial'],
          sharedStateUpdates: {
            ...context.sharedState,
            chapterContents: chapterContents
          }
        };
      }

      // Get current chapter to process
      const abstractionIndex = orderedIndices[currentChapterIndex];
      const abstraction = abstractionsData[abstractionIndex];
      const chapterNum = currentChapterIndex + 1;
      
      emitGraphStatus("WriteChapters", 50 + (currentChapterIndex / orderedIndices.length) * 40, 
        `Writing chapter ${chapterNum}/${orderedIndices.length}: ${abstraction.name}`);

      // Build chapter filenames for cross-references
      const chapter_filenames = orderedIndices.map((idx, i) => {
        const abs = abstractionsData[idx];
        const filename = `chapter_${i + 1}_${abs.name.toLowerCase().replace(/[^a-z0-9]/g, '_')}.md`;
        return filename;
      });

      // Build full chapter listing for context
      const full_chapter_listing = orderedIndices
        .map((idx, i) => `Chapter ${i + 1}: ${abstractionsData[idx].name} (${chapter_filenames[i]})`)
        .join('\n');

      // Get previous chapter info
      let previous_chapters_summary = '';
      if (currentChapterIndex > 0) {
        const prevChapters = chapterContents.slice(0, currentChapterIndex);
        previous_chapters_summary = prevChapters
          .map((ch, i) => `Chapter ${i + 1}: ${ch.name}`)
          .join('\n');
      }

      // Get content for this abstraction's files
      const related_files_content_map = getContentForIndices(files_data, abstraction.files);
      const file_context_str = Object.entries(related_files_content_map)
        .map(([fileKey, content]) => `${fileKey}:\n${content}`)
        .join('\n\n');

      // Prepare language instructions
      let language_instruction = '';
      let concept_details_note = '';
      let structure_note = '';
      let prev_summary_note = '';
      let instruction_lang_note = '';
      let link_lang_note = '';
      let code_comment_note = '';
      let mermaid_lang_note = '';
      let tone_note = '';
      let language_cap = 'English';

      if (lang.toLowerCase() !== 'english') {
        language_cap = lang.charAt(0).toUpperCase() + lang.slice(1);
        language_instruction = `IMPORTANT: Generate ALL content in **${language_cap}** language. Do NOT use English except for code and technical terms.\n\n`;
        concept_details_note = ` (in ${language_cap})`;
        structure_note = ` (in ${language_cap})`;
        prev_summary_note = ` (in ${language_cap})`;
        instruction_lang_note = ` (in ${language_cap})`;
        link_lang_note = ` (in ${language_cap})`;
        code_comment_note = ` (in ${language_cap})`;
        mermaid_lang_note = `Use ${language_cap} for diagram labels and text`;
        tone_note = ` (in ${language_cap})`;
      }

      console.log(`Writing chapter ${chapterNum}: ${abstraction.name}`);

      const prompt = buildPrompt(WRITE_CHAPTER_PROMPT, {
        language_instruction,
        project_name,
        abstraction_name: abstraction.name,
        chapter_num: chapterNum,
        concept_details_note,
        abstraction_description: abstraction.description,
        structure_note,
        full_chapter_listing,
        prev_summary_note,
        previous_chapters_summary,
        file_context_str,
        language_cap,
        instruction_lang_note,
        link_lang_note,
        code_comment_note,
        mermaid_lang_note,
        tone_note
      });

      emitGraphStatus("WriteChapters", 60 + (currentChapterIndex / orderedIndices.length) * 30, 
        `Generating content for chapter ${chapterNum}`);

      const chapterContent = await callLlm_openrouter({
        tutorial_id: tutorial_id as string,
        prompt,
        use_cache: useCache && (context.sharedState.retryCount || 0) === 0,
        user_id: user_id as string
      });

      // Add chapter to results
      const newChapter: ChapterContent = {
        name: abstraction.name,
        content: chapterContent
      };
      
      chapterContents.push(newChapter);

      emitGraphStatus("WriteChapters", 70 + (currentChapterIndex / orderedIndices.length) * 30, 
        `Completed chapter ${chapterNum}: ${abstraction.name}`);

      // Return result with next action (loop back to write next chapter)
      return {
        success: true,
        output: {
          currentChapter: newChapter,
          chapterContents: chapterContents,
          currentIndex: currentChapterIndex + 1,
          totalChapters: orderedIndices.length
        },
        events: [],
        nextActions: ['write-chapters'],
        sharedStateUpdates: {
          ...context.sharedState,
          chapterContents: chapterContents
        }
      };
    } catch (error) {
      // Return error result with retry logic
      const retryCount = (context.sharedState.retryCount as number) || 0;
      if (retryCount < this.maxRetries) {
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['write-chapters'],
          sharedStateUpdates: {
            ...context.sharedState,
            retryCount: retryCount + 1
          }
        };
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        sharedStateUpdates: context.sharedState
      };
    }
  }
}
