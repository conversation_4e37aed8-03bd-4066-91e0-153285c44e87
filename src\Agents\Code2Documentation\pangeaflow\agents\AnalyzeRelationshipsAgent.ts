// src/Agents/Code2Documentation/pangeaflow/agents/AnalyzeRelationshipsAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../../pangeaflow/pangeaflow';
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../../pocketflow/utils/buildPrompt";
import { ANALYZE_RELATIONSHIPS_PROMPT } from "../../prompts/analyzeRelationships";
import { emitGraphStatus, emitProgress } from "../../utils/events";
import { getContentForIndices } from "../../utils/fileHelpers";
import { Abstraction, RelationshipResult } from "../../types";
import yaml from 'js-yaml';

export class AnalyzeRelationshipsAgent extends AgentComponent {
  private maxRetries: number;
  private waitTime: number;

  constructor(eventBus: any, telemetry: any, maxRetries = 5, waitTime = 20) {
    super('analyze-relationships', eventBus, telemetry);
    this.maxRetries = maxRetries;
    this.waitTime = waitTime;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("AnalyzeRelationships", 0, "Starting relationship analysis");
    
    try {
      // Extract parameters from context
      const { 
        abstractions, 
        files, 
        project_name, 
        language, 
        use_cache, 
        user_id, 
        tutorial_id 
      } = context.metadata;

      const abstractionsData = abstractions as Abstraction[];
      const files_data = files as [string, string][];
      const lang = language as string ?? 'english';
      const useCache = use_cache as boolean ?? true;

      emitGraphStatus("AnalyzeRelationships", 10, `Analyzing relationships between ${abstractionsData.length} abstractions`);

      // Build abstraction listing
      const abstraction_listing = abstractionsData
        .map((abs, idx) => `${idx} # ${abs.name}`)
        .join('\n');

      emitGraphStatus("AnalyzeRelationships", 20, "Building context from abstraction files");

      // Build context string with abstraction details and relevant code
      let context = '';
      abstractionsData.forEach((abstraction, idx) => {
        context += `--- Abstraction ${idx}: ${abstraction.name} ---\n`;
        context += `Description: ${abstraction.description}\n`;
        context += `Files:\n`;

        // Get content for this abstraction's files
        const contentMap = getContentForIndices(files_data, abstraction.files);
        for (const [fileKey, content] of Object.entries(contentMap)) {
          context += `${fileKey}:\n${content}\n\n`;
        }
        context += '\n';
      });

      emitGraphStatus("AnalyzeRelationships", 30, "Preparing language instructions");

      // Prepare language instructions
      let language_instruction = '';
      let lang_hint = '';
      let list_lang_note = '';

      if (lang.toLowerCase() !== 'english') {
        const capLang = lang.charAt(0).toUpperCase() + lang.slice(1);
        language_instruction = `IMPORTANT: Generate the \`summary\` and \`label\` fields in **${capLang}** language. Do NOT use English for these fields.\n\n`;
        lang_hint = ` (in ${capLang})`;
        list_lang_note = ` (in ${capLang})`;
      }

      emitGraphStatus("AnalyzeRelationships", 40, "Starting LLM analysis to determine relationships");
      console.log('Analyzing relationships using LLM...');

      const prompt = buildPrompt(ANALYZE_RELATIONSHIPS_PROMPT, {
        project_name,
        abstraction_listing,
        context,
        language_instruction,
        lang_hint,
        list_lang_note
      });

      emitGraphStatus("AnalyzeRelationships", 50, "Sending request to LLM for relationship analysis");
      const response = await callLlm_openrouter({
        tutorial_id: tutorial_id as string,
        prompt, 
        use_cache: useCache && (context.metadata.retryCount || 0) === 0, 
        user_id: user_id as string
      });
      
      emitGraphStatus("AnalyzeRelationships", 60, "Received response from LLM, processing results");

      // Validation - Extract YAML between ```yaml
      const yaml_match = response.match(/```yaml([\s\S]*?)```/);
      if (!yaml_match) {
        emitGraphStatus("AnalyzeRelationships", 65, "Error: LLM output does not contain YAML block");
        throw new Error('LLM output does not contain YAML block');
      }

      const yaml_str = yaml_match[1].trim();
      emitGraphStatus("AnalyzeRelationships", 70, "Parsing YAML response");
      const parsed_result = yaml.load(yaml_str) as any;

      if (!parsed_result || typeof parsed_result !== 'object') {
        emitGraphStatus("AnalyzeRelationships", 75, "Error: LLM output is not a valid object");
        throw new Error('LLM output is not a valid object');
      }

      if (!parsed_result.summary || !Array.isArray(parsed_result.relationships)) {
        emitGraphStatus("AnalyzeRelationships", 77, "Error: LLM output missing required fields");
        throw new Error('LLM output missing required fields (summary, relationships)');
      }

      emitGraphStatus("AnalyzeRelationships", 80, `Found ${parsed_result.relationships.length} relationships`);

      // Validate and transform relationships
      emitGraphStatus("AnalyzeRelationships", 85, "Validating and transforming relationships");

      const relationships = parsed_result.relationships.map((rel: any) => {
        // Helper function for robust index parsing
        const parseIndex = (value: any, fieldName: string): number => {
          if (typeof value === 'number') {
            return value;
          }

          // Convert to string and apply robust parsing
          const valueStr = String(value).trim();

          // Handle various patterns:
          // - "8" -> 8
          // - "8 # filename" -> 8
          // - "8 in line Query Processing" -> 8
          // - "File Index 8: filename" -> 8
          // - "8: filename" -> 8

          let idx: number;
          if (valueStr.includes('#')) {
            // Pattern: "8 # filename" or "8#filename"
            idx = parseInt(valueStr.split('#')[0].trim(), 10);
          } else if (valueStr.includes(':')) {
            // Pattern: "8: filename" or "File Index 8: filename"
            const colonParts = valueStr.split(':');
            const beforeColon = colonParts[0].trim();
            // Extract number from "File Index 8" or just "8"
            const numberMatch = beforeColon.match(/(\d+)$/);
            if (numberMatch) {
              idx = parseInt(numberMatch[1], 10);
            } else {
              idx = parseInt(beforeColon, 10);
            }
          } else if (valueStr.includes(' in ')) {
            // Pattern: "8 in line Query Processing"
            const inParts = valueStr.split(' in ');
            idx = parseInt(inParts[0].trim(), 10);
          } else {
            // Extract first number found in the string
            const numberMatch = valueStr.match(/(\d+)/);
            if (numberMatch) {
              idx = parseInt(numberMatch[1], 10);
            } else {
              throw new Error(`No number found in ${fieldName}: ${valueStr}`);
            }
          }

          return idx;
        };

        // Parse from_abstraction and to_abstraction
        const from_idx = parseIndex(rel.from_abstraction, 'from_abstraction');
        const to_idx = parseIndex(rel.to_abstraction, 'to_abstraction');

        // Validate indices
        if (isNaN(from_idx) || isNaN(to_idx) ||
            from_idx < 0 || from_idx >= abstractionsData.length ||
            to_idx < 0 || to_idx >= abstractionsData.length) {
          throw new Error(`Invalid relationship indices: from=${from_idx}, to=${to_idx}`);
        }

        return {
          from: from_idx,
          to: to_idx,
          label: rel.label || 'Related'
        };
      });

      const relationshipResult: RelationshipResult = {
        summary: parsed_result.summary,
        details: relationships
      };

      emitGraphStatus("AnalyzeRelationships", 90, "Successfully validated all relationships");

      // Emit progress event
      emitProgress("Relationship Analysis", 60, `Analyzed ${relationships.length} relationships`);

      // Final graph status
      emitGraphStatus("AnalyzeRelationships", 100, "Relationship analysis complete");

      // Return success result with next action
      return {
        success: true,
        data: {
          relationships: relationshipResult,
          summary: parsed_result.summary
        },
        events: [],
        nextActions: ['order-chapters'],
        metadata: {
          ...context.metadata,
          relationships: relationshipResult,
          summary: parsed_result.summary
        }
      };
    } catch (error) {
      // Return error result with retry logic
      const retryCount = (context.metadata.retryCount as number) || 0;
      if (retryCount < this.maxRetries) {
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['analyze-relationships'],
          metadata: {
            ...context.metadata,
            retryCount: retryCount + 1
          }
        };
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}

