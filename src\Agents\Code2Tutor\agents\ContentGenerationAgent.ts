// src/Agents/Code2Tutor/agents/ContentGenerationAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../pangeaflow/pangeaflow';
import { SharedStore, TutorialSection, Exercise, CodeExample } from '../types';
import { emitAgentStatus, emitTutorProgress, emitSectionGenerated } from '../utils/events';
import { callLlm_openrouter } from '../../shared/callLlm_openrouter';
import { buildPrompt } from '../../../pocketflow/utils/buildPrompt';
import { CONTENT_GENERATION_PROMPT, EXERCISE_GENERATION_PROMPT } from '../prompts/'
/**
 * ContentGenerationAgent - Generates educational content for each tutorial section
 * 
 * This agent is responsible for:
 * - Generating detailed tutorial content for each section
 * - Creating practical exercises and coding challenges
 * - Developing code examples with explanations
 * - Ensuring content is appropriate for target audience
 * - Maintaining consistency across sections
 */
export class ContentGenerationAgent extends AgentComponent {
  private maxRetries = 3;
  private currentRetry = 0;

  constructor(eventBus: any, telemetry: any, maxRetries = 3) {
    super('content-generation', eventBus, telemetry, {
      stage: 'content-generation',
      progress: 0
    });
    this.maxRetries = maxRetries;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
  
    return this.withTelemetry('content-generation', async () => {
      emitAgentStatus('ContentGenerationAgent', 'starting', 0, 'Initializing content generation');
      
      const shared = context.sharedState.shared as SharedStore;
      
      try {
        // Validate inputs
        if (!shared.tutorial_structure || !shared.tutorial_structure.sections) {
          throw new Error('No tutorial structure available for content generation');
        }

        const sections = shared.tutorial_structure.sections;
        const generatedSections: TutorialSection[] = [];

        emitAgentStatus('ContentGenerationAgent', 'processing', 10, `Generating content for ${sections.length} sections`);
        emitTutorProgress('Content Generation', 10, `Starting content generation for ${sections.length} sections`);

        // Generate content for each section
        for (let i = 0; i < sections.length; i++) {
          const section = sections[i];
          const progressPercentage = 10 + (i / sections.length) * 80;

          emitAgentStatus('ContentGenerationAgent', 'processing', progressPercentage, `Generating content for: ${section.title}`);

          try {
            // Generate main content
            const content = await this.generateSectionContent(section, shared, i);
            
            // Generate exercises if enabled
            const exercises = shared.include_exercises 
              ? await this.generateExercises(section, shared)
              : [];

            // Generate code examples
            const codeExamples = await this.generateCodeExamples(section, shared);

            const generatedSection: TutorialSection = {
              ...section,
              content,
              exercises,
              codeExamples
            };

            generatedSections.push(generatedSection);
            emitSectionGenerated(generatedSection);

            emitTutorProgress('Content Generation', progressPercentage, `Completed section: ${section.title}`);

          } catch (error) {
            console.error(`Error generating content for section ${section.title}:`, error);
            
            // Create a fallback section
            const fallbackSection: TutorialSection = {
              ...section,
              content: this.createFallbackContent(section),
              exercises: [],
              codeExamples: []
            };
            
            generatedSections.push(fallbackSection);
          }
        }

        // Update shared store
        shared.sections = generatedSections;

        emitAgentStatus('ContentGenerationAgent', 'completed', 100, `Generated content for ${generatedSections.length} sections`);
        emitTutorProgress('Content Generation', 100, `Content generation completed for all ${generatedSections.length} sections`);

        this.emit('content.generated', {
          sections: generatedSections,
          sectionCount: generatedSections.length
        }, context.id);

        return {
          success: true,
          output: {
            sections: generatedSections,
            sectionCount: generatedSections.length
          },
          events: [],
          nextActions: ['assemble-tutorial'],
          sharedStateUpdates: {
            sectionsGenerated: generatedSections.length,
            includeExercises: shared.include_exercises,
            includeDiagrams: shared.include_diagrams
          }
        };

      } catch (error) {
        this.currentRetry++;
        
        if (this.currentRetry < this.maxRetries) {
          emitAgentStatus('ContentGenerationAgent', 'processing', 0, `Retry ${this.currentRetry}/${this.maxRetries}: ${error.message}`);
          
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 2000 * this.currentRetry));
          
          // Retry execution
          return this.execute(context);
        }

        emitAgentStatus('ContentGenerationAgent', 'error', 0, `Failed after ${this.maxRetries} attempts: ${error.message}`);
        
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['error'],
          sharedStateUpdates: { stage: 'content-generation', retries: this.currentRetry }
        };
      }
    });
  }

  /**
   * Generate main content for a tutorial section
   */
  private async generateSectionContent(section: TutorialSection, shared: SharedStore, sectionIndex: number): Promise<string> {
    // Find the concept for this section
    const concept = shared.concepts?.find(c => c.name === section.concept);
    if (!concept) {
      throw new Error(`Concept not found for section: ${section.concept}`);
    }

    // Prepare code context from relevant files
    const codeContext = this.prepareCodeContext(concept, shared.files || []);
    
    // Prepare previous sections context
    const previousContext = this.preparePreviousContext(shared.sections || [], sectionIndex);

    // Build prompt for content generation
    const prompt = buildPrompt(CONTENT_GENERATION_PROMPT, {
      concept_name: concept.name,
      section_title: section.title,
      target_audience: shared.target_audience || 'beginner',
      tutorial_format: shared.tutorial_format || 'guided',
      estimated_time: Math.round((shared.tutorial_structure?.metadata.estimatedTime || 60) / (shared.tutorial_structure?.sections.length || 1)),
      learning_goals: this.formatLearningGoals(concept),
      key_points: this.formatKeyPoints(concept),
      code_context: codeContext,
      previous_context: previousContext,
      include_exercises: shared.include_exercises ? 'true' : 'false',
      include_diagrams: shared.include_diagrams ? 'true' : 'false',
      language_instruction: this.getLanguageInstruction(shared.content_language),
      project_name: shared.project_name || 'Unknown Project'
    });

    // Call LLM for content generation
    const content = await callLlm_openrouter({
      tutorial_id: shared.tutorial_id,
      prompt,
      temperature: 0.7,
      model: "google/gemini-2.5-flash-preview-05-20",
      use_cache: shared.use_cache && this.currentRetry === 0,
      user_id: shared.user_id
    });

    return content;
  }

  /**
   * Generate exercises for a section
   */
  private async generateExercises(section: TutorialSection, shared: SharedStore): Promise<Exercise[]> {
    const concept = shared.concepts?.find(c => c.name === section.concept);
    if (!concept) return [];

    const codeContext = this.prepareCodeContext(concept, shared.files || []);

    const prompt = buildPrompt(EXERCISE_GENERATION_PROMPT, {
      concept_name: concept.name,
      concept_description: concept.description,
      target_audience: shared.target_audience || 'beginner',
      code_context: codeContext,
      language_instruction: this.getLanguageInstruction(shared.content_language)
    });

    try {
      const response = await callLlm_openrouter({
        tutorial_id: shared.tutorial_id,
        prompt,
        temperature: 0.8,
        model: "google/gemini-2.5-flash-preview-05-20",
        use_cache: shared.use_cache,
        user_id: shared.user_id
      });

      return this.parseExercisesFromResponse(response);
    } catch (error) {
      console.error('Error generating exercises:', error);
      return [];
    }
  }

  /**
   * Generate code examples for a section
   */
  private async generateCodeExamples(section: TutorialSection, shared: SharedStore): Promise<CodeExample[]> {
    const concept = shared.concepts?.find(c => c.name === section.concept);
    if (!concept || !concept.examples) return [];

    // Create code examples from concept examples
    return concept.examples.map((example, index) => ({
      id: `${section.id}_example_${index}`,
      title: `${concept.name} Example ${index + 1}`,
      code: this.extractCodeFromExample(example),
      language: shared.language || 'javascript',
      explanation: example,
      runnable: false,
      expectedOutput: undefined
    }));
  }

  /**
   * Prepare code context for a specific concept
   */
  private prepareCodeContext(concept: any, files: [string, string][]): string {
    const relevantFiles = concept.files
      .filter((index: number) => index < files.length)
      .map((index: number) => files[index]);

    if (relevantFiles.length === 0) return 'No specific code files available.';

    return relevantFiles
      .map(([path, content]) => `\n=== ${path} ===\n${content.substring(0, 2000)}`)
      .join('\n');
  }

  /**
   * Prepare context from previous sections
   */
  private preparePreviousContext(sections: TutorialSection[], currentIndex: number): string {
    if (currentIndex === 0) return 'This is the first section.';

    const previousSections = sections.slice(0, currentIndex);
    return previousSections
      .map(section => `${section.title}: ${section.concept}`)
      .join(', ');
  }

  /**
   * Format learning goals for a concept
   */
  private formatLearningGoals(concept: any): string {
    return [
      `Understand what ${concept.name} is and why it's important`,
      `Learn how to implement ${concept.name} in practice`,
      `Apply ${concept.name} to solve real problems`
    ].join('\n- ');
  }

  /**
   * Format key points for a concept
   */
  private formatKeyPoints(concept: any): string {
    const points = [
      `Core principles of ${concept.name}`,
      `Practical implementation patterns`,
      `Common use cases and examples`
    ];

    if (concept.prerequisites && concept.prerequisites.length > 0) {
      points.push(`Relationship to ${concept.prerequisites.join(', ')}`);
    }

    return points.join('\n- ');
  }

  /**
   * Parse exercises from LLM response
   */
  private parseExercisesFromResponse(response: string): Exercise[] {
    try {
      // Simple parsing - in a real implementation, you'd want more robust YAML parsing
      const exercises: Exercise[] = [];
      
      // For now, create a simple exercise
      exercises.push({
        id: `exercise_${Date.now()}`,
        type: 'coding',
        title: 'Practice Exercise',
        description: 'Apply what you\'ve learned in this hands-on exercise.',
        solution: '// Solution will be provided',
        hints: ['Start with the basic structure', 'Remember the key principles'],
        difficulty: 'easy'
      });

      return exercises;
    } catch (error) {
      console.error('Error parsing exercises:', error);
      return [];
    }
  }

  /**
   * Extract code from example text
   */
  private extractCodeFromExample(example: string): string {
    // Simple code extraction - look for code blocks or return the example as-is
    const codeMatch = example.match(/```[\w]*\n([\s\S]*?)\n```/);
    return codeMatch ? codeMatch[1] : example;
  }

  /**
   * Create fallback content for failed sections
   */
  private createFallbackContent(section: TutorialSection): string {
    return `# ${section.title}

This section covers the concept of **${section.concept}**.

*Content generation is in progress. Please check back later for the complete tutorial content.*

## Key Learning Points

- Understanding ${section.concept}
- Practical applications
- Implementation examples

## Next Steps

Continue to the next section to build upon these concepts.`;
  }

  /**
   * Get language instruction based on content language
   */
  private getLanguageInstruction(language: string): string {
    if (language && language.toLowerCase() !== 'english') {
      return `Write all content in ${language}. `;
    }
    return '';
  }
}
