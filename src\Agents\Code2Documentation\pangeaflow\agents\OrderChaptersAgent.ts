// src/Agents/Code2Documentation/pangeaflow/agents/OrderChaptersAgent.ts

import { AgentComponent, ExecutionContext, ExecutionResult } from '../../../../pangeaflow/pangeaflow';
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../../pocketflow/utils/buildPrompt";
import { ORDER_CHAPTERS_PROMPT } from "../../prompts/orderChapters";
import { emitGraphStatus, emitProgress } from "../../utils/events";
import { Abstraction, RelationshipResult } from "../../types";
import yaml from 'js-yaml';

export class OrderChaptersAgent extends AgentComponent {
  private maxRetries: number;
  private waitTime: number;

  constructor(eventBus: any, telemetry: any, maxRetries = 5, waitTime = 20) {
    super('order-chapters', eventBus, telemetry);
    this.maxRetries = maxRetries;
    this.waitTime = waitTime;
  }

  async execute(context: ExecutionContext): Promise<ExecutionResult> {
    emitGraphStatus("OrderChapters", 0, "Starting chapter ordering");

    try {
      console.log('OrderChaptersAgent: Executing with context:', {
        metadata: context.metadata,
        state: Array.from(context.state.entries())
      });

      // Extract parameters from context
      const {
        abstractions,
        relationships,
        summary,
        project_name,
        language,
        use_cache,
        user_id,
        tutorial_id
      } = context.metadata;

      // Try to get abstractions from state if not in metadata
      let abstractionsFromState = null;
      if (!abstractions && context.state.has('identify-abstractions')) {
        const identifyAbstractionsResult = context.state.get('identify-abstractions') as any;
        abstractionsFromState = identifyAbstractionsResult?.abstractions;
        console.log('OrderChaptersAgent: Found abstractions in state:', abstractionsFromState);
      }

      // Try to get relationships from state if not in metadata
      let relationshipsFromState = null;
      let summaryFromState = null;
      if (!relationships && context.state.has('analyze-relationships')) {
        const analyzeRelationshipsResult = context.state.get('analyze-relationships') as any;
        relationshipsFromState = analyzeRelationshipsResult?.relationships;
        summaryFromState = analyzeRelationshipsResult?.summary;
        console.log('OrderChaptersAgent: Found relationships in state:', relationshipsFromState);
        console.log('OrderChaptersAgent: Found summary in state:', summaryFromState);
      }

      const abstractionsData = (abstractions as Abstraction[]) || abstractionsFromState || [];
      const relationshipsData = (relationships as RelationshipResult) || relationshipsFromState || null;
      const summaryData = summary || summaryFromState || '';
      const lang = language as string ?? 'english';
      const useCache = use_cache as boolean ?? true;

      console.log('OrderChaptersAgent: Using abstractions:', abstractionsData);
      console.log('OrderChaptersAgent: Using relationships:', relationshipsData);
      console.log('OrderChaptersAgent: Using summary:', summaryData);

      if (!abstractionsData || abstractionsData.length === 0) {
        throw new Error('No abstractions found in context metadata or state');
      }

      emitGraphStatus("OrderChapters", 10, `Determining optimal order for ${abstractionsData.length} chapters`);

      // Build abstraction listing
      const abstraction_listing = abstractionsData
        .map((abs, idx) => `${idx} # ${abs.name}`)
        .join('\n');

      emitGraphStatus("OrderChapters", 20, "Building context from relationships and summary");

      // Build context string with relationships and summary
      let context_str = '';
      
      // Add project summary
      if (summaryData) {
        context_str += `Project Summary:\n${summaryData}\n\n`;
      }

      // Add relationships
      if (relationshipsData && relationshipsData.details) {
        context_str += `Relationships:\n`;
        relationshipsData.details.forEach(rel => {
          const fromName = abstractionsData[rel.from]?.name || `Index ${rel.from}`;
          const toName = abstractionsData[rel.to]?.name || `Index ${rel.to}`;
          context_str += `- ${fromName} ${rel.label} ${toName}\n`;
        });
        context_str += '\n';
      }

      emitGraphStatus("OrderChapters", 30, "Preparing language instructions");

      // Prepare language instructions
      let list_lang_note = '';
      if (lang.toLowerCase() !== 'english') {
        const capLang = lang.charAt(0).toUpperCase() + lang.slice(1);
        list_lang_note = ` (in ${capLang})`;
      }

      emitGraphStatus("OrderChapters", 40, "Starting LLM analysis to determine optimal chapter order");
      console.log('Determining chapter order using LLM...');

      const prompt = buildPrompt(ORDER_CHAPTERS_PROMPT, {
        project_name,
        list_lang_note,
        abstraction_listing,
        context: context_str
      });

      emitGraphStatus("OrderChapters", 50, "Sending request to LLM for chapter ordering");
      const response = await callLlm_openrouter({
        tutorial_id: tutorial_id as string,
        prompt, 
        use_cache: useCache && (context.metadata.retryCount || 0) === 0, 
        user_id: user_id as string
      });
      
      emitGraphStatus("OrderChapters", 60, "Received response from LLM, processing results");

      // Validation - Extract YAML between ```yaml
      const yamlMatch = response.trim().match(/```yaml([\s\S]*?)```/);
      if (!yamlMatch) {
        emitGraphStatus("OrderChapters", 65, "Error: LLM output does not contain YAML block");
        throw new Error('LLM output does not contain YAML block');
      }

      const yamlStr = yamlMatch[1].trim();
      emitGraphStatus("OrderChapters", 70, "Parsing YAML response");
      const orderedIndicesRaw = yaml.load(yamlStr) as any[];

      if (!Array.isArray(orderedIndicesRaw)) {
        emitGraphStatus("OrderChapters", 75, "Error: LLM output is not a list");
        throw new Error('LLM output is not a list');
      }

      emitGraphStatus("OrderChapters", 80, `Processing ${orderedIndicesRaw.length} ordered indices`);

      // Validate and transform indices
      const orderedIndices: number[] = [];
      
      for (const entry of orderedIndicesRaw) {
        try {
          let idx: number;

          if (typeof entry === 'number') {
            idx = entry;
          } else {
            // Convert to string and apply robust parsing
            const entryStr = String(entry).trim();

            // Handle various patterns:
            // - "8" -> 8
            // - "8 # filename" -> 8
            // - "8 in line Query Processing" -> 8
            // - "File Index 8: filename" -> 8
            // - "8: filename" -> 8

            if (entryStr.includes('#')) {
              // Pattern: "8 # filename" or "8#filename"
              idx = parseInt(entryStr.split('#')[0].trim(), 10);
            } else if (entryStr.includes(':')) {
              // Pattern: "8: filename" or "File Index 8: filename"
              const colonParts = entryStr.split(':');
              const beforeColon = colonParts[0].trim();
              // Extract number from "File Index 8" or just "8"
              const numberMatch = beforeColon.match(/(\d+)$/);
              if (numberMatch) {
                idx = parseInt(numberMatch[1], 10);
              } else {
                idx = parseInt(beforeColon, 10);
              }
            } else if (entryStr.includes(' in ')) {
              // Pattern: "8 in line Query Processing"
              const inParts = entryStr.split(' in ');
              idx = parseInt(inParts[0].trim(), 10);
            } else {
              // Extract first number found in the string
              const numberMatch = entryStr.match(/(\d+)/);
              if (numberMatch) {
                idx = parseInt(numberMatch[1], 10);
              } else {
                throw new Error(`No number found in entry: ${entryStr}`);
              }
            }
          }

          if (isNaN(idx) || idx < 0 || idx >= abstractionsData.length) {
            emitGraphStatus("OrderChapters", 82, `Error: Invalid abstraction index ${idx}`);
            throw new Error(`Invalid abstraction index ${idx}. Valid range: 0-${abstractionsData.length - 1}`);
          }

          orderedIndices.push(idx);
        } catch (error) {
          emitGraphStatus("OrderChapters", 83, `Error: Could not parse index from entry: ${entry}`);
          throw new Error(`Could not parse index from entry: ${entry}`);
        }
      }

      // Validate that all abstractions are included
      const uniqueIndices = new Set(orderedIndices);
      if (uniqueIndices.size !== abstractionsData.length) {
        emitGraphStatus("OrderChapters", 85, "Error: Not all abstractions are included in the order");
        throw new Error(`Expected ${abstractionsData.length} unique indices, got ${uniqueIndices.size}`);
      }

      for (let i = 0; i < abstractionsData.length; i++) {
        if (!uniqueIndices.has(i)) {
          emitGraphStatus("OrderChapters", 86, `Error: Missing abstraction index ${i}`);
          throw new Error(`Missing abstraction index ${i} in the ordered list`);
        }
      }

      emitGraphStatus("OrderChapters", 90, "Successfully validated chapter order");

      // Emit progress event
      emitProgress("Chapter Ordering", 80, `Determined order for ${orderedIndices.length} chapters`);

      // Final graph status
      emitGraphStatus("OrderChapters", 100, "Chapter ordering complete");

      // Return success result with next action
      return {
        success: true,
        data: {
          orderedAbstractions: orderedIndices
        },
        events: [],
        nextActions: ['write-chapters'],
        metadata: {
          ...context.metadata,
          orderedAbstractions: orderedIndices
        }
      };
    } catch (error) {
      // Return error result with retry logic
      const retryCount = (context.metadata.retryCount as number) || 0;
      if (retryCount < this.maxRetries) {
        return {
          success: false,
          error: error as Error,
          events: [],
          nextActions: ['order-chapters'],
          metadata: {
            ...context.metadata,
            retryCount: retryCount + 1
          }
        };
      }
      
      return {
        success: false,
        error: error as Error,
        events: [],
        nextActions: ['error'],
        metadata: context.metadata
      };
    }
  }
}
